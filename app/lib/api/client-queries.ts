import { useQuery, useMutation, useInfiniteQuery } from "@tanstack/react-query";
import { useState, useEffect } from "react";
import { HOSTS_CONFIG } from "~/config/hosts";
import { useAppContext } from "~/lib/providers/app-context";

import type {
  GroupResponse,
  GroupsResponse,
  GroupCohortsResponse,
  CohortResponse,
  CourseResponse,
  LessonResponse,
  MyGroupsResponse,
  LiveClassesResponse,
  ProfileResponse,
  APIResponse,
  UserProfile,
  APISuccessResponse,
  ProfileByIdResponse,
  LiveClassResponse,
  NotificationsResponse,
  ModuleResponse,
} from "~/lib/api/types";
import { HttpError } from "~/lib/api/types";
import { queryClient } from "~/lib/providers/query-client";

const API_URL = HOSTS_CONFIG.api;

function useProfile() {
  return useQuery<APISuccessResponse<UserProfile>>({
    queryKey: ["profile"],
    queryFn: async () => {
      const response = await fetch(`${API_URL}/profile`);
      const data = (await response.json()) as APIResponse<UserProfile>;
      if (!data.success) {
        throw new HttpError(data.data.message, response.status);
      }
      return data;
    },
  });
}

function useEditProfile() {
  return useMutation({
    mutationFn: async ({
      firstName,
      lastName,
      age,
      avatarURL,
      bio,
      gender,
      interestedTopics,
      phoneNumber,
    }: {
      firstName: string;
      lastName: string;
      age?: number;
      avatarURL?: string;
      bio?: string;
      gender: string | null;
      interestedTopics?: string[];
      phoneNumber?: string;
    }) => {
      const response = await fetch(`${API_URL}/profile`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          firstName,
          lastName,
          age,
          avatarURL,
          bio,
          gender,
          interestedTopics,
          phoneNumber,
        }),
      });
      const data = await response.json();
      if (!data.success) {
        throw new HttpError(data.data.message, response.status);
      }
      return data;
    },
    onMutate: async (newProfile) => {
      // Cancel any outgoing refetches
      // (so they don't overwrite our optimistic update)
      await queryClient.cancelQueries({ queryKey: ["profile"] });

      // Snapshot the previous value
      const previousProfile = queryClient.getQueryData<ProfileResponse>([
        "profile",
      ]);

      // Optimistically update to the new value
      queryClient.setQueryData(["profile"], (old: ProfileResponse) => ({
        ...old,
        data: {
          ...old.data,
          ...newProfile,
        },
      }));

      // Return a context object with the snapshotted value
      return { previousProfile };
    },
    onError: (err, newProfile, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousProfile) {
        queryClient.setQueryData(["profile"], context.previousProfile);
      }
    },
    onSettled: () => {
      queryClient.invalidateQueries({
        queryKey: ["profile"],
      });
    },
  });
}

function useGroups(limit: number = 20) {
  return useInfiniteQuery<GroupsResponse>({
    queryKey: ["groups"],
    queryFn: async ({ pageParam = 0 }) => {
      const response = await fetch(
        `${API_URL}/groups?limit=${limit}&offset=${pageParam}`
      );
      const data = await response.json();
      return data;
    },
    getNextPageParam: (lastPage, allPages) => {
      if (!lastPage.success) return undefined;

      const totalFetched = allPages.length * limit;
      const hasMore = totalFetched < lastPage.total;

      return hasMore ? totalFetched : undefined;
    },
    initialPageParam: 0,
  });
}

function useMyGroups() {
  return useQuery<{
    success: boolean;
    groups: {
      byId: { [id: string]: MyGroupsResponse["data"]["groups"][0] };
      order: string[];
    };
  }>({
    queryKey: ["my", "groups"],
    queryFn: async () => {
      try {
        const response = await fetch(`${API_URL}/me/groups`);
        const data = (await response.json()) as MyGroupsResponse;

        if (data.success) {
          // Normalization
          let myGroups: {
            byId: {
              [externalId: string]: MyGroupsResponse["data"]["groups"][0];
            };
            order: string[];
          } = {
            byId: {},
            order: [],
          };
          data.data.groups.forEach((group) => {
            myGroups.byId[group.externalId] = group;
            myGroups.order.push(group.externalId);
          });

          return { success: true, groups: myGroups };
        }

        return { success: false, groups: { byId: {}, order: [] } };
      } catch (e) {
        throw e;
      }
    },
  });
}

function useGroup(id: string | number | null) {
  return useQuery<GroupResponse>({
    queryKey: ["groups", id],
    queryFn: async () => {
      const response = await fetch(`${API_URL}/groups/${id}`);
      const data = await response.json();
      return data;
    },
    enabled: !!id,
  });
}

function useGroupCohorts(groupId: string | number) {
  return useQuery<GroupCohortsResponse>({
    queryKey: ["groups", groupId, "cohorts"],
    queryFn: async () => {
      const response = await fetch(`${API_URL}/groups/${groupId}/cohorts`);
      const data = await response.json();
      return data;
    },
  });
}

function useCohort(
  groupId: string | number | null,
  cohortId: string | number | null
) {
  return useQuery<CohortResponse>({
    queryKey: ["groups", groupId, "cohorts", cohortId],
    queryFn: async () => {
      const response = await fetch(
        `${API_URL}/groups/${groupId}/cohorts/${cohortId}`
      );
      const data = await response.json();
      return data;
    },
    enabled: !!groupId && !!cohortId, // Only run the query when both groupId and cohortId are set
  });
}

function useCourse(id: string | number) {
  return useQuery<CourseResponse>({
    queryKey: ["courses", id],
    queryFn: async () => {
      const response = await fetch(`${API_URL}/courses/${id}`);
      const data = await response.json();
      return data;
    },
  });
}

function useLesson(
  courseId: string | number,
  sectionId: string | number,
  lessonId: string | number
) {
  return useQuery<LessonResponse>({
    queryKey: ["courses", courseId, "sections", sectionId, "lessons", lessonId],
    queryFn: async () => {
      const response = await fetch(
        `${API_URL}/courses/${courseId}/sections/${sectionId}/lessons/${lessonId}`
      );
      const data = await response.json();
      return data;
    },
  });
}

function useLiveClasses(
  groupId: string | number,
  cohortId: string | number,
  moduleId: string | number,
  isPresent: boolean,
  limit: number = 20
) {
  return useInfiniteQuery<LiveClassesResponse>({
    queryKey: [
      "groups",
      groupId,
      "cohorts",
      cohortId,
      "modules",
      moduleId,
      "live-classes",
      "isPresent",
      isPresent,
    ],
    queryFn: async ({ pageParam = 0 }) => {
      const response = await fetch(
        `${API_URL}/groups/${groupId}/cohorts/${cohortId}/modules/${moduleId}/live-classes?isPresent=${isPresent}&limit=${limit}&offset=${pageParam}`
      );
      const data = await response.json();
      return data;
    },
    getNextPageParam: (lastPage, allPages) => {
      if (!lastPage.success) return undefined;

      const totalFetched = allPages.length * limit;
      const hasMore = totalFetched < lastPage.total;

      return hasMore ? totalFetched : undefined;
    },
    initialPageParam: 0,
  });
}

function useLiveClass(
  groupId: string | number,
  cohortId: string | number,
  moduleId: string | number,
  liveClassId: string | number
) {
  return useQuery<LiveClassResponse>({
    queryKey: [
      "groups",
      groupId,
      "cohorts",
      cohortId,
      "modules",
      moduleId,
      "live-classes",
      liveClassId,
    ],
    queryFn: async () => {
      const response = await fetch(
        `${API_URL}/groups/${groupId}/cohorts/${cohortId}/modules/${moduleId}/live-classes/${liveClassId}`
      );
      const data = await response.json();
      return data;
    },
  });
}

function useMyCohort(externalCohortId: string) {
  return useQuery<CohortResponse>({
    queryKey: ["me", "cohorts", externalCohortId],
    queryFn: async () => {
      const response = await fetch(`${API_URL}/me/cohorts/${externalCohortId}`);
      const data = await response.json();
      return data;
    },
  });
}

function useMyCohortModule(
  externalGroupId: string,
  externalCohortId: string,
  moduleId: string
) {
  return useQuery<ModuleResponse>({
    queryKey: ["me", "cohorts", externalCohortId, "modules", moduleId],
    queryFn: async () => {
      const response = await fetch(
        `${API_URL}/groups/${externalGroupId}/cohorts/${externalCohortId}/modules/${moduleId}`
      );
      const data = await response.json();
      return data;
    },
  });
}

function useJoinCohort() {
  return useMutation({
    mutationFn: async ({
      groupId,
      cohortId,
    }: {
      groupId: string;
      cohortId: string;
    }) => {
      const response = await fetch(
        `${API_URL}/groups/${groupId}/cohorts/${cohortId}/join`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );
      const data = await response.json();
      return data;
    },
  });
}

function useGenerateGetStreamToken() {
  return useMutation({
    mutationFn: async () => {
      const response = await fetch(`${API_URL}/getstream/token/generate`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });
      const data = await response.json();

      return data;
    },
  });
}

function useCompleteLesson() {
  return useMutation({
    mutationFn: async ({
      courseId,
      sectionId,
      lessonId,
    }: {
      courseId: string;
      sectionId: string;
      lessonId: string;
    }) => {
      const response = await fetch(
        `${API_URL}/courses/${courseId}/sections/${sectionId}/lessons/${lessonId}/complete`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );
      const data = await response.json();
      return { courseId, sectionId, lessonId };
    },
    // onMutate: async (data) => {
    //   // Cancel any outgoing refetches
    //   // (so they don't overwrite our optimistic update)
    //   await queryClient.cancelQueries({ queryKey: ["courses", data?.courseId] });

    //   // Snapshot the previous value
    //   const previousCourse = queryClient.getQueryData([
    //     "courses",
    //     data?.courseId,
    //   ]);

    //   // Optimistically update to the new value
    //   queryClient.setQueryData(["courses", data?.courseId], (old: CourseResponse) => {
    //     if (!old?.data) return old;

    //     return {
    //       ...old,
    //       data: {
    //         ...old.data,
    //         sections: old.data.sections.map(section => {
    //           if (section.id === data?.sectionId) {
    //             return {
    //               ...section,
    //               lessons: section.lessons.map(lesson => {
    //                 if (lesson.id === data?.lessonId) {
    //                   return {
    //                     ...lesson,
    //                     isCompleted: true,
    //                   };
    //                 }
    //                 return lesson;
    //               })
    //             };
    //           }
    //           return section;
    //         })
    //       }
    //     };
    //   });

    //   // Return a context object with the snapshotted value
    //   return { previousCourse };
    // },
    onSuccess: (data) => {
      if (!data?.courseId || !data?.sectionId || !data?.lessonId) return;

      // Invalidate both the lesson and course queries
      queryClient.invalidateQueries({
        queryKey: [
          "courses",
          data?.courseId,
          "sections",
          data?.sectionId,
          "lessons",
          data?.lessonId,
        ],
      });

      queryClient.invalidateQueries({
        queryKey: ["courses", data?.courseId],
      });
    },
  });
}

function useRegisterPushNotificationToken() {
  return useMutation({
    mutationFn: async ({
      platform,
      token,
    }: {
      platform: string;
      token: string;
    }) => {
      const response = await fetch(`${API_URL}/notifications/device-tokens`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ token, platform }),
      });
      const data = await response.json();
      return data;
    },
  });
}

function useProfileById(supertokensUserId: string) {
  return useQuery<APISuccessResponse<ProfileByIdResponse>>({
    queryKey: ["profile", supertokensUserId],
    queryFn: async () => {
      const response = await fetch(`${API_URL}/profile/${supertokensUserId}`);
      const data = (await response.json()) as APIResponse<ProfileByIdResponse>;
      if (!data.success) {
        throw new HttpError(data.data.message, response.status);
      }
      return data;
    },
    enabled: !!supertokensUserId,
  });
}

function useRegisterLiveClass() {
  return useMutation({
    mutationFn: async ({
      groupId,
      cohortId,
      moduleId,
      liveClassId,
    }: {
      groupId: string;
      cohortId: string;
      moduleId: string;
      liveClassId: string;
    }) => {
      const response = await fetch(
        `${API_URL}/groups/${groupId}/cohorts/${cohortId}/modules/${moduleId}/live-classes/${liveClassId}/register`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );
      const data = (await response.json()) as APIResponse<null>;
      return { ...data, groupId, cohortId, moduleId, liveClassId };
    },
    onSuccess: ({ success, groupId, cohortId, moduleId, liveClassId }) => {
      if (!success) return;

      // Invalidate both the lesson and course queries
      queryClient.invalidateQueries({
        queryKey: [
          "groups",
          groupId,
          "cohorts",
          cohortId,
          "modules",
          moduleId,
          "live-classes",
          liveClassId,
        ],
      });

      // invalidate the isPresent query because the user only able to
      // register to a live class that is not over yet
      queryClient.invalidateQueries({
        queryKey: [
          "groups",
          groupId,
          "cohorts",
          cohortId,
          "modules",
          moduleId,
          "live-classes",
          "isPresent",
          true,
        ],
      });
    },
  });
}

function useNotifications(limit: number = 20) {
  return useInfiniteQuery<NotificationsResponse>({
    queryKey: ["notifications"],
    queryFn: async ({ pageParam = 0 }) => {
      const response = await fetch(
        `${API_URL}/me/notifications?limit=${limit}&offset=${pageParam}`
      );
      const data = await response.json();
      console.log("Notifications", data);
      return data;
    },
    getNextPageParam: (lastPage, allPages) => {
      if (!lastPage.success) return undefined;

      const totalFetched = allPages.length * limit;
      const hasMore = totalFetched < lastPage.data.total;

      return hasMore ? totalFetched : undefined;
    },
    initialPageParam: 0,
  });
}

function useReadNotification() {
  return useMutation({
    mutationFn: async ({ notificationId }: { notificationId: string }) => {
      const response = await fetch(
        `${API_URL}/notifications/${notificationId}/read`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );
      const data = (await response.json()) as APIResponse<null>;
      return { ...data, notificationId };
    },
    onMutate: ({ notificationId }) => {},
    onSuccess: ({ success, notificationId }) => {
      if (!success) return;

      queryClient.invalidateQueries({
        queryKey: ["notifications"],
      });
    },
  });
}

function useGetUploadUrl() {
  return useMutation({
    mutationFn: async ({
      contentType,
      fileName,
    }: {
      contentType: string;
      fileName: string;
    }) => {
      const response = await fetch(`${API_URL}/internal/storage/upload`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          contentType,
          fileName,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new HttpError(
          result.message || "Failed to get upload URL",
          response.status
        );
      }

      // Return the nested data object
      return result.data as { cdnUrl: string; uploadUrl: string };
    },
  });
}

function useUploadAvatar() {
  return useMutation({
    mutationFn: async ({
      uploadUrl,
      uri,
      fileType,
    }: {
      uploadUrl: string;
      uri: string;
      fileType: string;
    }) => {
      try {
        // Try two different approaches for uploading

        // Approach 1: FormData (if the API expects multipart/form-data)
        try {
          const formData = new FormData();
          formData.append("file", {
            uri,
            type: fileType,
            name: `avatar.${fileType.split("/")[1]}`,
          } as any);

          const uploadResponse = await fetch(uploadUrl, {
            method: "PUT",
            body: formData,
          });

          if (uploadResponse.ok) {
            return uploadResponse;
          }
        } catch (formDataError) {
          // FormData approach failed, try raw binary
        }

        // Approach 2: Raw binary data (common for S3 presigned URLs)
        const response = await fetch(uri);
        if (!response.ok) {
          throw new HttpError("Failed to read image file", response.status);
        }

        const blob = await response.blob();

        const uploadResponse = await fetch(uploadUrl, {
          method: "PUT",
          headers: {
            "Content-Type": fileType,
          },
          body: blob,
        });

        if (!uploadResponse.ok) {
          const errorText = await uploadResponse
            .text()
            .catch(() => "No error text available");
          throw new HttpError(
            `Failed to upload avatar: ${uploadResponse.status} ${uploadResponse.statusText}`,
            uploadResponse.status
          );
        }

        return uploadResponse;
      } catch (error) {
        throw error;
      }
    },
  });
}

// Optimized GetStream feed hooks
function useFeedActivities(feedGroup: string, feedId: string) {
  const { streamClient } = useAppContext();

  return useInfiniteQuery({
    queryKey: ["feed", feedGroup, feedId],
    queryFn: async ({ pageParam = undefined }) => {
      if (!streamClient) {
        throw new Error("Stream client not initialized");
      }

      const feed = streamClient.feed(feedGroup, feedId);

      // Optimized query parameters based on Oracle recommendations
      const response = await feed.get({
        limit: 10, // Reduced from 25 for faster initial load
        id_lt: pageParam,
        enrich: true,
        reactions: {
          own: true,
          counts: true,
          recent: {
            kinds: ["comment"],
            limit: 2, // Only show 2 recent comments to avoid extra API calls
          },
        },
      });

      return response;
    },
    initialPageParam: undefined,
    getNextPageParam: (lastPage: any) => {
      if (lastPage.results.length < 10) return undefined;
      return lastPage.results[lastPage.results.length - 1]?.id;
    },
    enabled: !!streamClient,
    staleTime: 1000 * 60 * 5, // 5 minutes cache
    refetchOnWindowFocus: false,
  });
}

// Real-time feed subscription hook
function useFeedRealtime(feedGroup: string, feedId: string) {
  const { streamClient } = useAppContext();
  const [isConnected, setIsConnected] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);

  useEffect(() => {
    if (!streamClient) return;

    let subscription: any = null;

    const setupRealtime = async () => {
      try {
        const feed = streamClient.feed(feedGroup, feedId);

        // Subscribe to real-time updates
        subscription = feed.subscribe((data: any) => {
          console.log("🔄 Real-time feed update:", data);
          setLastUpdate(new Date());

          // Handle different types of real-time events
          if (data.new && data.new.length > 0) {
            console.log("✨ New activities:", data.new);
            // Invalidate queries to refetch with new data
            queryClient.invalidateQueries({
              queryKey: ["feed", feedGroup, feedId],
            });
          }

          if (data.deleted && data.deleted.length > 0) {
            console.log("🗑️ Deleted activities:", data.deleted);
            // Invalidate queries to remove deleted activities
            queryClient.invalidateQueries({
              queryKey: ["feed", feedGroup, feedId],
            });
          }

          // Handle reaction updates (likes, comments)
          if (data.updated && data.updated.length > 0) {
            console.log("🔄 Updated activities:", data.updated);
            // Invalidate queries to refresh reaction counts
            queryClient.invalidateQueries({
              queryKey: ["feed", feedGroup, feedId],
            });
          }
        });

        setIsConnected(true);
        console.log(`🔗 Connected to real-time feed: ${feedGroup}:${feedId}`);
      } catch (error) {
        console.error("❌ Failed to setup real-time feed subscription:", error);
        setIsConnected(false);
      }
    };

    setupRealtime();

    // Cleanup subscription
    return () => {
      if (subscription && typeof subscription.cancel === "function") {
        subscription.cancel();
        console.log(
          `🔌 Disconnected from real-time feed: ${feedGroup}:${feedId}`
        );
      }
      setIsConnected(false);
    };
  }, [streamClient, feedGroup, feedId]);

  return { isConnected, lastUpdate };
}

function useFeedLike(feedGroup: string, feedId: string) {
  const { streamClient } = useAppContext();

  return useMutation({
    mutationFn: async ({
      activityId,
      isLiked,
      reactionId,
    }: {
      activityId: string;
      isLiked: boolean;
      reactionId?: string;
    }) => {
      if (!streamClient) {
        throw new Error("Stream client not initialized");
      }

      if (isLiked && reactionId) {
        return streamClient.reactions.delete(reactionId);
      } else {
        return streamClient.reactions.add("like", activityId);
      }
    },
    onMutate: async ({ activityId, isLiked }) => {
      // Optimistically update the cache
      const queryKey = ["feed", feedGroup, feedId];
      await queryClient.cancelQueries({ queryKey });

      const previousData = queryClient.getQueryData(queryKey);

      queryClient.setQueryData(queryKey, (old: any) => {
        if (!old) return old;

        return {
          ...old,
          pages: old.pages.map((page: any) => ({
            ...page,
            results: page.results.map((activity: any) => {
              if (activity.id !== activityId) return activity;

              const currentLikeCount = activity.reaction_counts?.like || 0;
              const hasOwnLike = activity.own_reactions?.like?.length > 0;

              return {
                ...activity,
                reaction_counts: {
                  ...activity.reaction_counts,
                  like: isLiked ? currentLikeCount - 1 : currentLikeCount + 1,
                },
                own_reactions: {
                  ...activity.own_reactions,
                  like: isLiked ? [] : [{ id: "temp-id" }],
                },
              };
            }),
          })),
        };
      });

      return { previousData };
    },
    onError: (err, variables, context) => {
      // Rollback on error
      if (context?.previousData) {
        queryClient.setQueryData(
          ["feed", feedGroup, feedId],
          context.previousData
        );
      }
    },
    onSettled: () => {
      // Refetch to sync with server
      queryClient.invalidateQueries({ queryKey: ["feed", feedGroup, feedId] });
    },
  });
}

function useFeedComment(feedGroup: string, feedId: string) {
  const { streamClient, userId } = useAppContext();
  const { data: profileData } = useProfile();

  return useMutation({
    mutationFn: async ({
      activityId,
      text,
    }: {
      activityId: string;
      text: string;
    }) => {
      if (!streamClient) {
        throw new Error("Stream client not initialized");
      }

      return streamClient.reactions.add("comment", activityId, { text });
    },
    onMutate: async ({ activityId, text }) => {
      // Optimistically update the cache
      const queryKey = ["feed", feedGroup, feedId];
      await queryClient.cancelQueries({ queryKey });

      const previousData = queryClient.getQueryData(queryKey);

      queryClient.setQueryData(queryKey, (old: any) => {
        if (!old) return old;

        return {
          ...old,
          pages: old.pages.map((page: any) => ({
            ...page,
            results: page.results.map((activity: any) => {
              if (activity.id !== activityId) return activity;

              const currentCommentCount =
                activity.reaction_counts?.comment || 0;

              return {
                ...activity,
                reaction_counts: {
                  ...activity.reaction_counts,
                  comment: currentCommentCount + 1,
                },
              };
            }),
          })),
        };
      });

      return { previousData };
    },
    onError: (err, variables, context) => {
      // Rollback on error
      if (context?.previousData) {
        queryClient.setQueryData(
          ["feed", feedGroup, feedId],
          context.previousData
        );
      }
    },
    onSettled: (data, error, variables) => {
      // Refetch to sync with server
      queryClient.invalidateQueries({ queryKey: ["feed", feedGroup, feedId] });
      // Also invalidate the specific comment cache for this activity
      queryClient.invalidateQueries({
        queryKey: ["feed-comments", variables.activityId],
      });
    },
  });
}

function useFeedPost(feedGroup: string, feedId: string) {
  const { streamClient, userId } = useAppContext();

  return useMutation({
    mutationFn: async ({
      text,
      attachment,
      ogData,
    }: {
      text: string;
      attachment?: {
        type: string;
        url: string;
        fileName?: string;
      };
      ogData?: any;
    }) => {
      if (!streamClient) {
        throw new Error("Stream client not initialized");
      }

      const feed = streamClient.feed(feedGroup, feedId);
      const timestamp = new Date().toISOString();

      const activityData: any = {
        verb: "post",
        message: text,
        object: `cohort:${feedId}`,
        to: [`${feedGroup}:${feedId}`],
        time: timestamp,
        // Add foreign_id to enable updates - using timestamp + userId for uniqueness
        foreign_id: `post:${userId}:${Date.now()}`,
      };

      if (attachment) {
        activityData.attachments = [
          {
            type: attachment.type === "image" ? "image" : "file",
            [attachment.type === "image" ? "image_url" : "asset_url"]:
              attachment.url,
            custom: attachment.fileName
              ? { fileName: attachment.fileName }
              : {},
          },
        ];
      }

      // Add Open Graph data if available
      if (ogData) {
        activityData.og = ogData;
      }

      return feed.addActivity(activityData);
    },
    onSuccess: () => {
      // Invalidate and refetch feed data
      queryClient.invalidateQueries({ queryKey: ["feed", feedGroup, feedId] });
    },
  });
}

function useFeedEditPost(feedGroup: string, feedId: string) {
  const { streamClient, userId } = useAppContext();

  return useMutation({
    mutationFn: async ({
      activityId,
      foreignId,
      time,
      text,
      attachment,
      ogData,
    }: {
      activityId: string;
      foreignId: string;
      time: string;
      text: string;
      attachment?: {
        type: string;
        url: string;
        fileName?: string;
      };
      ogData?: any;
    }) => {
      if (!streamClient) {
        throw new Error("Stream client not initialized");
      }

      // Prepare the activity data for the backend API
      const activityData: any = {
        actor: userId,
        verb: "post",
        object: `cohort:${feedId}`,
        target: `${feedGroup}:${feedId}`,
        time: time,
        foreignId: foreignId,
        extra: {
          message: text,
        },
      };

      // Add attachment data if provided
      if (attachment) {
        activityData.extra.attachments = [
          {
            type: attachment.type === "image" ? "image" : "file",
            [attachment.type === "image" ? "image_url" : "asset_url"]:
              attachment.url,
            custom: attachment.fileName
              ? { fileName: attachment.fileName }
              : {},
          },
        ];
      }

      // Add Open Graph data if available
      if (ogData) {
        activityData.extra.og = ogData;
      }

      // Call the backend API to update the activity
      const response = await fetch(`${API_URL}/getstream/activities`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          activities: [activityData],
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new HttpError(
          result.message || "Failed to update post",
          response.status
        );
      }

      return result;
    },
    onMutate: async ({ activityId, text, attachment, ogData }) => {
      // Optimistically update the cache
      const queryKey = ["feed", feedGroup, feedId];
      await queryClient.cancelQueries({ queryKey });

      const previousData = queryClient.getQueryData(queryKey);

      queryClient.setQueryData(queryKey, (old: any) => {
        if (!old) return old;

        return {
          ...old,
          pages: old.pages.map((page: any) => ({
            ...page,
            results: page.results.map((activity: any) => {
              if (activity.id !== activityId) return activity;

              const updatedActivity = {
                ...activity,
                message: text,
              };

              // Update attachments
              if (attachment) {
                if (attachment.type === "image") {
                  updatedActivity.attachments = [
                    {
                      type: "image",
                      image_url: attachment.url,
                      custom: attachment.fileName
                        ? { fileName: attachment.fileName }
                        : {},
                    },
                  ];
                } else {
                  updatedActivity.attachments = [
                    {
                      type: "file",
                      asset_url: attachment.url,
                      custom: attachment.fileName
                        ? { fileName: attachment.fileName }
                        : {},
                    },
                  ];
                }
              } else {
                // Remove attachments if no attachment provided
                updatedActivity.attachments = [];
                // Also remove legacy image field
                delete updatedActivity.image;
              }

              // Update Open Graph data
              if (ogData) {
                updatedActivity.og = ogData;
              } else {
                // Remove OG data if not provided
                delete updatedActivity.og;
              }

              return updatedActivity;
            }),
          })),
        };
      });

      return { previousData };
    },
    onError: (err, variables, context) => {
      // Rollback on error
      if (context?.previousData) {
        queryClient.setQueryData(
          ["feed", feedGroup, feedId],
          context.previousData
        );
      }
    },
    onSettled: () => {
      // Refetch to sync with server
      queryClient.invalidateQueries({ queryKey: ["feed", feedGroup, feedId] });
    },
  });
}

function useFeedDeletePost(feedGroup: string, feedId: string) {
  return useMutation({
    mutationFn: async ({
      activityId,
      foreignId,
    }: {
      activityId: string;
      foreignId?: string;
    }) => {
      // Prepare query parameters for the DELETE request
      const params = new URLSearchParams({
        feedGroup,
        feedId,
      });

      // Use foreignId if available, otherwise use activityId
      if (foreignId) {
        params.append("foreignId", foreignId);
      } else {
        params.append("activityId", activityId);
      }

      // Call the backend API to delete the activity
      const response = await fetch(
        `${API_URL}/getstream/activities?${params.toString()}`,
        {
          method: "DELETE",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.ok) {
        const result = await response.json().catch(() => ({}));
        throw new HttpError(
          result.message || "Failed to delete post",
          response.status
        );
      }

      return { activityId, foreignId };
    },
    onMutate: async ({ activityId }) => {
      // Cancel any outgoing refetches
      const queryKey = ["feed", feedGroup, feedId];
      await queryClient.cancelQueries({ queryKey });

      // Snapshot the previous value
      const previousData = queryClient.getQueryData(queryKey);

      // Optimistically remove the activity from the cache
      queryClient.setQueryData(queryKey, (old: any) => {
        if (!old) return old;

        return {
          ...old,
          pages: old.pages.map((page: any) => ({
            ...page,
            results: page.results.filter(
              (activity: any) => activity.id !== activityId
            ),
          })),
        };
      });

      return { previousData };
    },
    onError: (err, variables, context) => {
      // Rollback on error
      if (context?.previousData) {
        queryClient.setQueryData(
          ["feed", feedGroup, feedId],
          context.previousData
        );
      }
    },
    onSettled: () => {
      // Refetch to sync with server
      queryClient.invalidateQueries({ queryKey: ["feed", feedGroup, feedId] });
    },
  });
}

function useFeedComments(activityId: string) {
  const { streamClient } = useAppContext();

  return useQuery({
    queryKey: ["feed-comments", activityId],
    queryFn: async () => {
      if (!streamClient) {
        throw new Error("Stream client not initialized");
      }

      const response = await streamClient.reactions.filter({
        activity_id: activityId,
        kind: "comment",
        limit: 50,
      });

      return response.results;
    },
    enabled: false, // Only fetch when explicitly triggered
    staleTime: 1000 * 60 * 2, // 2 minutes cache
  });
}

export {
  useProfile,
  useEditProfile,
  useGroups,
  useMyGroups,
  useGroup,
  useGroupCohorts,
  useCourse,
  useCohort,
  useLesson,
  useLiveClasses,
  useLiveClass,
  useMyCohort,
  useMyCohortModule,
  useJoinCohort,
  useGenerateGetStreamToken,
  useCompleteLesson,
  useRegisterPushNotificationToken,
  useProfileById,
  useRegisterLiveClass,
  useNotifications,
  useReadNotification,
  useGetUploadUrl,
  useUploadAvatar,
  useFeedActivities,
  useFeedRealtime,
  useFeedLike,
  useFeedComment,
  useFeedPost,
  useFeedEditPost,
  useFeedDeletePost,
  useFeedComments,
};
